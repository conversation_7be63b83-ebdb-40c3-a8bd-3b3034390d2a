import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import '../widgets/signin.dart';

/// Écran de demande de renouvellement de permis
///
/// Permet à l'utilisateur de soumettre une demande de renouvellement
/// avec les informations suivantes :
/// - Durée de renouvellement (6, 12, ou 24 mois)
/// - Raison du renouvellement
/// - Document PDF justificatif
class RenewRequestScreen extends StatefulWidget {
  /// ID du permis à renouveler
  final int permitId;

  const RenewRequestScreen({
    super.key,
    required this.permitId,
  });

  @override
  _RenewRequestScreenState createState() => _RenewRequestScreenState();
}

class _RenewRequestScreenState extends State<RenewRequestScreen> {
  final TextEditingController _remarkController = TextEditingController();
  final List<PlatformFile> _selectedFiles = [];
  final _formKey = GlobalKey<FormState>();
  final _storage = const FlutterSecureStorage();
  String? _renewalDuration = '6';
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _remarkController.dispose();
    super.dispose();
  }

  /// Récupère le token d'authentification depuis le stockage sécurisé
  Future<String?> _getAuthToken() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null || token.isEmpty) {
        // Log pour le développement - à remplacer par un système de logging en production
        debugPrint('No auth token found in FlutterSecureStorage');
        return null;
      }
      return token;
    } catch (e) {
      // Log pour le développement - à remplacer par un système de logging en production
      debugPrint('Error reading auth token from FlutterSecureStorage: $e');
      return null;
    }
  }

  /// Valide les données du formulaire avant soumission
  bool _validateFormData() {
    // Vérification du fichier PDF
    if (_selectedFiles.isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'no_files_uploaded');
      });
      return false;
    }

    // Vérification de la durée de renouvellement
    if (_renewalDuration == null || _renewalDuration!.isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'select_renewal_duration');
      });
      return false;
    }

    // Vérification de la raison
    if (_remarkController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'reason_required');
      });
      return false;
    }

    // Vérification de la taille du fichier
    final file = _selectedFiles.first;
    if (file.size > 10 * 1024 * 1024) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'file_too_large');
      });
      return false;
    }

    // Vérification que les bytes du fichier sont disponibles
    if (file.bytes == null) {
      setState(() {
        _errorMessage = AppLocalizations.tr(context, 'file_read_error');
      });
      return false;
    }

    return true;
  }

  /// Affiche un message d'erreur et navigue vers l'écran de connexion si nécessaire
  void _handleAuthError() {
    if (!mounted) return;

    setState(() {
      _errorMessage = AppLocalizations.tr(context, 'auth_token_missing');
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocalizations.tr(context, 'auth_token_missing')),
        backgroundColor: AppColors.error,
        action: SnackBarAction(
          label: AppLocalizations.tr(context, 'login'),
          textColor: AppColors.pureWhite,
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const SignInScreen()),
            );
          },
        ),
      ),
    );
  }

  Future<void> _pickFiles() async {
    if (!mounted) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        withData: true,
      );

      if (!mounted) return;

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Vérification de la taille du fichier (10 MB max)
        if (file.size > 10 * 1024 * 1024) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'file_too_large'))),
          );
          return;
        }

        // Vérification que les bytes du fichier sont disponibles
        if (file.bytes == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'file_read_error'))),
          );
          return;
        }

        setState(() {
          _selectedFiles.clear();
          _selectedFiles.add(file);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'file_selected_success')),
            backgroundColor: AppColors.success,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'no_file_selected')),
            backgroundColor: AppColors.warning,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${AppLocalizations.tr(context, 'file_pick_error')}: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _submitForm() async {
    // Validation des fichiers
    if (_selectedFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.tr(context, 'no_files_uploaded')),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    // Validation du formulaire
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Récupération du token d'authentification
      final authToken = await _getAuthToken();
      if (authToken == null) {
        _handleAuthError();
        return;
      }

      // Création de la requête multipart
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/${widget.permitId}/renew'),
      );

      // Ajout des headers
      request.headers.addAll({
        'Accept': 'application/json',
        'Authorization': 'Bearer $authToken',
      });

      // Ajout des champs du formulaire
      request.fields.addAll({
        'renewalDuration': _renewalDuration ?? '6',
        'reason': _remarkController.text.trim(),
      });

      // Ajout du fichier PDF
      final file = _selectedFiles.first;
      if (file.bytes != null) {
        request.files.add(
          http.MultipartFile.fromBytes(
            'renewal_pdf',
            file.bytes!,
            filename: file.name,
          ),
        );
      } else {
        throw Exception('File bytes are null');
      }

      // Envoi de la requête
      final response = await request.send();
      final responseBody = await http.Response.fromStream(response);

      if (!mounted) return;

      // Traitement de la réponse
      if (response.statusCode == 200 || response.statusCode == 201) {
        // Succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.tr(context, 'form_submitted_success')),
            backgroundColor: AppColors.success,
          ),
        );

        // Réinitialisation du formulaire
        setState(() {
          _selectedFiles.clear();
          _remarkController.clear();
          _renewalDuration = '6';
        });
      } else {
        // Erreur de l'API
        String errorMessage = _parseErrorMessage(responseBody.body, response.statusCode);
        setState(() {
          _errorMessage = errorMessage;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      final errorMessage = '${AppLocalizations.tr(context, 'form_submission_error')}: $e';
      setState(() {
        _errorMessage = errorMessage;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Parse le message d'erreur depuis la réponse de l'API
  String _parseErrorMessage(String responseBody, int statusCode) {
    try {
      final jsonResponse = jsonDecode(responseBody);
      return jsonResponse['message'] ??
             '${AppLocalizations.tr(context, 'form_submission_failed')}: $statusCode';
    } catch (_) {
      return '${AppLocalizations.tr(context, 'form_submission_failed')}: $statusCode';
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'renew_request'),
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: Constants.screenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.tr(context, 'renew_request'),
                style: ThemeHelper.getSectionTitleStyle(context),
              ),
              const SizedBox(height: Constants.mediumSpacing),

              // Affichage des messages d'erreur
              if (_errorMessage != null) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: Constants.mediumSpacing),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.error.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: AppColors.error,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: ThemeHelper.getSubtitleStyle(context).copyWith(
                            color: AppColors.error,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 18),
                        color: AppColors.error,
                        onPressed: () => setState(() => _errorMessage = null),
                      ),
                    ],
                  ),
                ),
              ],
              Card(
                elevation: Constants.cardTheme.elevation,
                shape: Constants.cardTheme.shape,
                color: ThemeHelper.getColors(context).card,
                child: Padding(
                  padding: Constants.cardPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildRequiredDocumentsList(isArabic),
                      const SizedBox(height: Constants.largeSpacing),
                      _buildFileUploadSection(isArabic),
                      const SizedBox(height: Constants.largeSpacing),
                      _buildRenewalDurationField(isArabic),
                      const SizedBox(height: Constants.largeSpacing),
                      _buildRemarkField(isArabic),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: Constants.extraLargeSpacing),
              _buildActionButton(isArabic, context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequiredDocumentsList(bool isArabic) {
    final documents = [
      AppLocalizations.tr(context, 'document_id_copy'),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: documents.map((doc) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: 6,
                    left: isArabic ? 8 : 0,
                    right: isArabic ? 0 : 8,
                  ),
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    doc,
                    style: ThemeHelper.getSubtitleStyle(context),
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                ),
              ],
            ),
          )).toList(),
    );
  }

  Widget _buildFileUploadSection(bool isArabic) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(
          Icons.upload_file,
          size: 48,
          color: AppColors.info,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Text(
          AppLocalizations.tr(context, 'upload_file'),
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(fontSize: 18),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'click_to_select_file'),
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Center(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _pickFiles,
            style: ThemeHelper.getSecondaryButtonStyle(context).copyWith(
              minimumSize: WidgetStateProperty.all(const Size(200, 40)),
            ),
            child: Text(AppLocalizations.tr(context, 'select_file_button')),
          ),
        ),
        if (_selectedFiles.isNotEmpty) ...[
          const SizedBox(height: Constants.mediumSpacing),
          Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeHelper.getColors(context).backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'selected_file'),
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.picture_as_pdf,
                        size: 20,
                        color: AppColors.darkGray,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          _selectedFiles.first.name,
                          style: ThemeHelper.getSubtitleStyle(context),
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20),
                        onPressed: () => setState(() => _selectedFiles.clear()),
                        color: AppColors.darkGray,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'pdf_only'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: 12,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRenewalDurationField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'renewal_duration'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _renewalDuration,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: ['6', '12', '24'].map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text('$value ${AppLocalizations.tr(context, 'months')}'),
            );
          }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              _renewalDuration = newValue;
            });
          },
          validator: (value) {
            if (value == null) {
              return AppLocalizations.tr(context, 'select_renewal_duration');
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildRemarkField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'reason'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _remarkController,
          decoration: InputDecoration(
            hintText: AppLocalizations.tr(context, 'enter_reason'),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          maxLines: 4,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppLocalizations.tr(context, 'reason_required');
            }
            return null;
          },
          textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
        ),
      ],
    );
  }

  Widget _buildActionButton(bool isArabic, BuildContext context) {
    final bool isButtonEnabled = !_isLoading &&
                                _selectedFiles.isNotEmpty &&
                                _renewalDuration != null &&
                                _remarkController.text.trim().isNotEmpty;

    return Center(
      child: Column(
        children: [
          ElevatedButton(
            onPressed: isButtonEnabled ? _submitForm : null,
            style: ThemeHelper.getPrimaryButtonStyle(context).copyWith(
              minimumSize: WidgetStateProperty.all(const Size(200, 48)),
              backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
                if (states.contains(WidgetState.disabled)) {
                  return AppColors.lightGray;
                }
                return AppColors.primaryOrange;
              }),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: AppColors.pureWhite,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    AppLocalizations.tr(context, 'submit'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),

          // Texte d'aide si le bouton est désactivé
          if (!isButtonEnabled && !_isLoading) ...[
            const SizedBox(height: 8),
            Text(
              _getButtonDisabledReason(),
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                fontSize: 12,
                color: AppColors.darkGray,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// Retourne la raison pour laquelle le bouton est désactivé
  String _getButtonDisabledReason() {
    if (_selectedFiles.isEmpty) {
      return AppLocalizations.tr(context, 'select_file_first');
    }
    if (_renewalDuration == null) {
      return AppLocalizations.tr(context, 'select_renewal_duration');
    }
    if (_remarkController.text.trim().isEmpty) {
      return AppLocalizations.tr(context, 'enter_reason_first');
    }
    return '';
  }
}