import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';

class RenewRequestScreen extends StatefulWidget {
  final int permitId; // Dynamic permit ID
  const RenewRequestScreen({super.key, this.permitId = 1}); // Default to 1 for testing

  @override
  _RenewRequestScreenState createState() => _RenewRequestScreenState();
}

class _RenewRequestScreenState extends State<RenewRequestScreen> {
  final List<PlatformFile> _selectedFiles = [];
  final _formKey = GlobalKey<FormState>();
  final _storage = const FlutterSecureStorage();
  String? _renewalDuration = '6';
  bool _isLoading = false;

  // Constant reason as required by the backend
  static const String _constantReason = "Need more time to complete the project";

  /// Get authentication token from secure storage
  Future<String?> _getAuthToken() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token == null) {
        print('No auth token found in FlutterSecureStorage');
      }
      return token;
    } catch (e) {
      print('Error reading auth token from FlutterSecureStorage: $e');
      return null;
    }
  }

  Future<void> _pickFiles() async {
    if (!mounted) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.size > 10 * 1024 * 1024) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'file_too_large'))),
          );
          return;
        }
        if (file.bytes == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'file_read_error'))),
          );
          return;
        }

        setState(() {
          _selectedFiles.clear();
          _selectedFiles.add(file);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.tr(context, 'file_selected_success'))),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.tr(context, 'no_file_selected'))),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.tr(context, 'file_pick_error') + ': $e')),
      );
    }
  }

  Future<void> _submitForm() async {
    // Validate that PDF file is uploaded
    if (_selectedFiles.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.tr(context, 'no_files_uploaded'))),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Get authentication token
        final authToken = await _getAuthToken();
        if (authToken == null || authToken.isEmpty) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'not_authenticated')),
              backgroundColor: AppColors.error,
            ),
          );
          setState(() {
            _isLoading = false;
          });
          return;
        }

        var request = http.MultipartRequest(
          'POST',
          Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/${widget.permitId}/renew'),
        );

        // Add headers with dynamic token
        request.headers['Accept'] = 'application/json';
        request.headers['Authorization'] = 'Bearer $authToken';

        // Add form fields with constant reason
        request.fields['renewalDuration'] = _renewalDuration ?? '6';
        request.fields['reason'] = _constantReason;

        // Add the PDF file
        if (_selectedFiles.isNotEmpty) {
          final file = _selectedFiles.first;
          if (file.bytes != null) {
            request.files.add(
              http.MultipartFile.fromBytes(
                'renewal_pdf',
                file.bytes!,
                filename: file.name,
              ),
            );
          } else {
            throw Exception('File bytes are null');
          }
        }

        // Log request details for debugging
        print('Request URL: ${request.url}');
        print('Request Fields: ${request.fields}');
        print('Request Files: ${request.files.map((f) => f.filename).toList()}');

        // Send the request
        final response = await request.send();
        final responseBody = await http.Response.fromStream(response);

        print('Response Status: ${response.statusCode}');
        print('Response Body: ${responseBody.body}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocalizations.tr(context, 'form_submitted_success')),
              backgroundColor: AppColors.success,
            ),
          );
          setState(() {
            _selectedFiles.clear();
            _renewalDuration = '6';
          });
        } else {
          if (!mounted) return;
          // Parse the response body to extract the error message
          String errorMessage = AppLocalizations.tr(context, 'form_submission_failed');
          try {
            final jsonResponse = jsonDecode(responseBody.body);
            errorMessage = jsonResponse['message'] ?? errorMessage;

            // Handle specific error for permit ownership
            if (errorMessage.contains('You can only renew your own permits')) {
              errorMessage = 'You can only renew your own permits';
            }
          } catch (_) {
            errorMessage = '${AppLocalizations.tr(context, 'form_submission_failed')}: ${response.statusCode}';
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: AppColors.error,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } catch (e) {
        print('Error: $e');
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.tr(context, 'form_submission_error')}: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'renew_request'),
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: Constants.screenPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.tr(context, 'renew_request'),
                style: ThemeHelper.getSectionTitleStyle(context),
              ),
              const SizedBox(height: Constants.mediumSpacing),
              Card(
                elevation: Constants.cardTheme.elevation,
                shape: Constants.cardTheme.shape,
                color: ThemeHelper.getColors(context).card,
                child: Padding(
                  padding: Constants.cardPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildRequiredDocumentsList(isArabic),
                      const SizedBox(height: Constants.largeSpacing),
                      _buildFileUploadSection(isArabic),
                      const SizedBox(height: Constants.largeSpacing),
                      _buildRenewalDurationField(isArabic),
                      const SizedBox(height: Constants.largeSpacing),
                      _buildReasonDisplay(isArabic),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: Constants.extraLargeSpacing),
              _buildActionButton(isArabic, context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequiredDocumentsList(bool isArabic) {
    final documents = [
      AppLocalizations.tr(context, 'document_id_copy'),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: documents.map((doc) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: 6,
                    left: isArabic ? 8 : 0,
                    right: isArabic ? 0 : 8,
                  ),
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    doc,
                    style: ThemeHelper.getSubtitleStyle(context),
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                ),
              ],
            ),
          )).toList(),
    );
  }

  Widget _buildFileUploadSection(bool isArabic) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(
          Icons.upload_file,
          size: 48,
          color: AppColors.info,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Text(
          AppLocalizations.tr(context, 'upload_file'),
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(fontSize: 18),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'click_to_select_file'),
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Center(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _pickFiles,
            style: ThemeHelper.getSecondaryButtonStyle(context).copyWith(
              minimumSize: WidgetStateProperty.all(const Size(200, 40)),
            ),
            child: Text(AppLocalizations.tr(context, 'select_file_button')),
          ),
        ),
        if (_selectedFiles.isNotEmpty) ...[
          const SizedBox(height: Constants.mediumSpacing),
          Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeHelper.getColors(context).backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'selected_file'),
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.picture_as_pdf,
                        size: 20,
                        color: AppColors.darkGray,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          _selectedFiles.first.name,
                          style: ThemeHelper.getSubtitleStyle(context),
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20),
                        onPressed: () => setState(() => _selectedFiles.clear()),
                        color: AppColors.darkGray,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'pdf_only'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: 12,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRenewalDurationField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'renewal_duration'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _renewalDuration,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: ['6', '12', '24'].map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text('$value ${AppLocalizations.tr(context, 'months')}'),
            );
          }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              _renewalDuration = newValue;
            });
          },
          validator: (value) {
            if (value == null) {
              return AppLocalizations.tr(context, 'select_renewal_duration');
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildReasonDisplay(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'reason'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ThemeHelper.getColors(context).backgroundSecondary,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: ThemeHelper.getColors(context).textSecondary.withOpacity(0.3),
            ),
          ),
          child: Text(
            _constantReason,
            style: ThemeHelper.getSubtitleStyle(context),
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'This reason is automatically set for all renewal requests',
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: 12,
            color: ThemeHelper.getColors(context).textSecondary,
            fontStyle: FontStyle.italic,
          ),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
      ],
    );
  }

  Widget _buildActionButton(bool isArabic, BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: (_isLoading || _selectedFiles.isEmpty) ? null : _submitForm,
        style: ThemeHelper.getPrimaryButtonStyle(context).copyWith(
          minimumSize: WidgetStateProperty.all(const Size(200, 40)),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: AppColors.pureWhite,
                  strokeWidth: 2,
                ),
              )
            : Text(AppLocalizations.tr(context, 'submit')),
      ),
    );
  }
}